import os
from transformers import AutoTokenizer
from vllm import LLM,SamplingParams
import Levenshtein
import json
model_path = "/data/jiangh/nju-devops/Zeta-finetune/sft_params/"
tokenizer_path = "/data/jiangh/nju-devops/Zeta-finetune/sft_params/"
# model_path = "/data/jiangh/nju-devops/LLM/Qwen2.5-Coder-7B-Instruct/"
# tokenizer_path = "/data/jiangh/nju-devops/LLM/Qwen2.5-Coder-7B-Instruct/"
llm=LLM(model_path)
tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)

def read_jsonl(file_path):
    """
    从JSONL文件中读取数据，并根据特定标记截取prompt和label。
    """
    prompts = []
    labels = []
    
    # 关键标记，必须与训练时使用的完全一致
    start_marker = "<|editable_region_start|>"
    end_marker = "<|editable_region_end|>"

    with open(file_path, 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            try:
                data_line = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行跳过无效JSON: {e}")
                continue

            required_keys = ['input', 'output', 'events']
            if not all(key in data_line for key in required_keys):
                print(f"第 {line_num} 行跳过缺失字段的行（需要包含{required_keys}）")
                continue

            # --- [!!] 核心修改部分 [!!] ---

            # 1. 获取完整的原始output作为label的来源
            full_output_for_label = data_line['output']

            # 2. 查找start_marker的位置
            start_index = full_output_for_label.find(start_marker)
            if start_index == -1:
                print(f"警告: 第 {line_num} 行的label中未找到start_marker，已跳过。")
                continue

            # 3. 从start_marker开始，截取剩余部分
            focused_region = full_output_for_label[start_index:]

            # 4. 在截取后的区域内查找结束标记
            end_index = focused_region.find(end_marker)
            if end_index == -1:
                print(f"警告: 第 {line_num} 行的label中未找到 '{end_marker}'，已跳过。")
                continue
            
            # 5. 执行最终截取，包含结束标记本身
            # `end_index` 是 `focused_region` 中 `end_marker` 的起始位置
            # 所以需要加上 `end_marker` 的长度来包含它
            final_label = focused_region[:end_index + len(end_marker)]
            
            instruction = f"User Edits:\n{data_line['events']}\n\nUser Excerpt:\n{data_line['input']}"
            # 构建符合官方模板的对话列表
            messages = [
                {"role": "system", "content": "You are an AI code completion assistant. Your sole function is to predict and output the final intended code based on the provided context. You will be given \"User Edits\" (a log of changes) and a \"User Excerpt\" (the current code). The \"User Excerpt\" will contain a marked editable region and the user's cursor position. Your output must be only the complete, final code for the editable region, enclosed by <|editable_region_start|> and <|editable_region_end|>. Do not include any other text, explanation, or characters."},
                {"role": "user", "content": instruction}
                # {"role": "assistant", "content": output+ EOS_TOKEN}  # 添加结束符,推理时不需要
            ]
            # 使用tokenizer的内置模板，并自动添加 special tokens (包括EOS)
            text = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            prompts.append(text) # 假设 make_prompt 函数已定义

            start_index = final_label.find(start_marker)
            final_label = final_label[start_index:]  # 截取从start_marker开始的部分
            end_index = final_label.find(end_marker)
            final_label = final_label[:end_index + len(end_marker)]  # 包含结束标记本身
            labels.append(final_label)

    return prompts, labels


datas, labels = read_jsonl("/data/jiangh/nju-devops/datasets/zeta/eval.jsonl")
sum_score,num = 0.0,0.0
for prompt,label in zip(datas, labels):
    sampling_params = SamplingParams(
        temperature=0,  # 温度：控制生成的随机性。值越低，生成结果越确定；值越高，越随机。
        top_p=0.9,     # Top-p (nucleus sampling)：从累积概率超过 p 的词汇中进行采样。
        max_tokens=1024, # 最大生成 token 数：限制生成文本的长度，防止无限生成。
        n=1,                  # 为每个 prompt 生成 n 个独立的输出。
        skip_special_tokens=False,
        # presence_penalty=0.1, # 存在惩罚：轻微惩罚已出现过的 token，鼓励模型谈论新主题。
        # frequency_penalty=0.1 # 频率惩罚：轻微惩罚高频出现的 token，减少重复词语。
    )
    outputs = llm.generate([prompt], sampling_params)
    predicted_text = outputs[0].outputs[0].text
    print(f'预测的文本:\n{predicted_text}')
    score = Levenshtein.ratio(predicted_text, label) * 100
    sum_score += score
    num += 1
    print(f'第{num}次的得分: {score:.2f}')  # 保留2位小数显示
print(f'平均得分: {sum_score/num}')