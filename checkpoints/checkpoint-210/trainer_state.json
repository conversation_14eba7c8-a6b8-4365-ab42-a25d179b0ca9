{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 2.0, "eval_steps": 500, "global_step": 210, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09523809523809523, "grad_norm": 0.1831156313419342, "learning_rate": 9.523809523809524e-05, "loss": 0.0547, "mean_token_accuracy": 0.982977956533432, "num_tokens": 30568.0, "step": 10}, {"epoch": 0.19047619047619047, "grad_norm": 0.1383102983236313, "learning_rate": 9.047619047619048e-05, "loss": 0.0363, "mean_token_accuracy": 0.9903926193714142, "num_tokens": 63282.0, "step": 20}, {"epoch": 0.2857142857142857, "grad_norm": 0.14665678143501282, "learning_rate": 8.571428571428571e-05, "loss": 0.0352, "mean_token_accuracy": 0.989738267660141, "num_tokens": 93376.0, "step": 30}, {"epoch": 0.38095238095238093, "grad_norm": 0.06595475226640701, "learning_rate": 8.095238095238096e-05, "loss": 0.0311, "mean_token_accuracy": 0.9906832575798035, "num_tokens": 122801.0, "step": 40}, {"epoch": 0.47619047619047616, "grad_norm": 0.09222688525915146, "learning_rate": 7.619047619047618e-05, "loss": 0.0263, "mean_token_accuracy": 0.9931761026382446, "num_tokens": 154011.0, "step": 50}, {"epoch": 0.5714285714285714, "grad_norm": 0.1502283215522766, "learning_rate": 7.142857142857143e-05, "loss": 0.0304, "mean_token_accuracy": 0.9916923642158508, "num_tokens": 32568.0, "step": 60}, {"epoch": 0.6666666666666666, "grad_norm": 0.08808600157499313, "learning_rate": 6.666666666666667e-05, "loss": 0.0343, "mean_token_accuracy": 0.9911836802959442, "num_tokens": 64361.0, "step": 70}, {"epoch": 0.7619047619047619, "grad_norm": 0.30602994561195374, "learning_rate": 6.19047619047619e-05, "loss": 0.0368, "mean_token_accuracy": 0.9891195833683014, "num_tokens": 95230.0, "step": 80}, {"epoch": 0.8571428571428571, "grad_norm": 0.13957591354846954, "learning_rate": 5.714285714285714e-05, "loss": 0.0351, "mean_token_accuracy": 0.9905946135520936, "num_tokens": 126147.0, "step": 90}, {"epoch": 0.9523809523809523, "grad_norm": 0.18253390491008759, "learning_rate": 5.2380952380952384e-05, "loss": 0.0289, "mean_token_accuracy": 0.9930551826953888, "num_tokens": 154488.0, "step": 100}, {"epoch": 1.0476190476190477, "grad_norm": 0.03254246339201927, "learning_rate": 4.761904761904762e-05, "loss": 0.0183, "mean_token_accuracy": 0.9963106274604797, "num_tokens": 183369.0, "step": 110}, {"epoch": 1.1428571428571428, "grad_norm": 0.06001304090023041, "learning_rate": 4.2857142857142856e-05, "loss": 0.009, "mean_token_accuracy": 0.9975128412246704, "num_tokens": 211145.0, "step": 120}, {"epoch": 1.2380952380952381, "grad_norm": 0.06732483208179474, "learning_rate": 3.809523809523809e-05, "loss": 0.008, "mean_token_accuracy": 0.9979953825473785, "num_tokens": 238934.0, "step": 130}, {"epoch": 1.3333333333333333, "grad_norm": 0.012754511088132858, "learning_rate": 3.3333333333333335e-05, "loss": 0.0077, "mean_token_accuracy": 0.9988045871257782, "num_tokens": 267594.0, "step": 140}, {"epoch": 1.4285714285714286, "grad_norm": 0.05312815308570862, "learning_rate": 2.857142857142857e-05, "loss": 0.008, "mean_token_accuracy": 0.9971679627895356, "num_tokens": 302726.0, "step": 150}, {"epoch": 1.5238095238095237, "grad_norm": 0.1061057299375534, "learning_rate": 2.380952380952381e-05, "loss": 0.0061, "mean_token_accuracy": 0.9981588363647461, "num_tokens": 331228.0, "step": 160}, {"epoch": 1.619047619047619, "grad_norm": 0.09087105095386505, "learning_rate": 1.9047619047619046e-05, "loss": 0.0051, "mean_token_accuracy": 0.998425567150116, "num_tokens": 362748.0, "step": 170}, {"epoch": 1.7142857142857144, "grad_norm": 0.10264018923044205, "learning_rate": 1.4285714285714285e-05, "loss": 0.0074, "mean_token_accuracy": 0.997740375995636, "num_tokens": 399780.0, "step": 180}, {"epoch": 1.8095238095238095, "grad_norm": 0.013125686906278133, "learning_rate": 9.523809523809523e-06, "loss": 0.0047, "mean_token_accuracy": 0.9982463121414185, "num_tokens": 428934.0, "step": 190}, {"epoch": 1.9047619047619047, "grad_norm": 0.08113914728164673, "learning_rate": 4.7619047619047615e-06, "loss": 0.0081, "mean_token_accuracy": 0.9970927596092224, "num_tokens": 459661.0, "step": 200}, {"epoch": 2.0, "grad_norm": 0.10758176445960999, "learning_rate": 0.0, "loss": 0.0083, "mean_token_accuracy": 0.997235631942749, "num_tokens": 492457.0, "step": 210}], "logging_steps": 10, "max_steps": 210, "num_input_tokens_seen": 0, "num_train_epochs": 2, "save_steps": 50, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1299715522560.0, "train_batch_size": 2, "trial_name": null, "trial_params": null}