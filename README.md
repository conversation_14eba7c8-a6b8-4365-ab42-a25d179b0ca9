### 环境
- 32 服务器，conda activate edit-pred-model && deepspeed --num_gpus=2 train_scripts/base_sft_lora.py
- 2 * 4090 * 21GB each, dram 59GB

### 参数
- 某些参数、数据集路径、模型路径都是硬编码到文件开头部分，没有开放CLI接口
## train_scripts
- `base_sft_lora.py`: 可选择合成数据集或zeta开源数据集,base模型lora脚本
- `Instruct_sft_lora`:只可选择zeta开源数据集,Instruct模型lora脚本，目前暂没使用Instruct模型,修改了提示词
- `dpo_lora`: dpo数据集暂未构造，但可根据zeta开源数据集进行lora微调
## test_scripts
- 测试集都是拿zeta开源数据集的eval.json计算平均编辑相似度和BLEU
- `test_zeta_dataset-Instruct.py`:针对Instruct模型进行测试，单个执行非批量
- `test_zeta_dataset-base.py`:针对base模型进行测试，单个执行非批量
- `test_zeta_dataset-base-batch.py`:针对base模型进行批量测试，通常用这个
## deepspeed_cofig
- deepspeed配置文件
## data_synthesis
- 用于数据合成,详细见/data_synthesis/readme.md
## compare_scripts
- 将两个模型的针对zeta验证集的生成结果存为一个parquet方便进行对比