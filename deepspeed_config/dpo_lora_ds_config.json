{"train_batch_size": 2, "train_micro_batch_size_per_gpu": 1, "gradient_accumulation_steps": 1, "gradient_clipping": 1.0, "zero_optimization": {"stage": 2, "stage3_gather_16bit_weights_on_model_save": true, "offload_optimizer": {"device": "cpu", "pin_memory": true}, "allgather_partitions": true, "allgather_bucket_size": 500000000.0, "reduce_scatter": true, "reduce_bucket_size": 500000000.0, "contiguous_gradients": true, "overlap_comm": true}, "optimizer": {"type": "AdamW", "params": {"lr": 5e-05, "betas": [0.9, 0.999], "eps": 1e-08, "weight_decay": 0.01}}, "bf16": {"enabled": true}, "fp16": {"enabled": false, "initial_scale_power": 16}}