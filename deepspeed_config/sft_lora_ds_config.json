{"train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "gradient_accumulation_steps": "auto", "gradient_clipping": 1.0, "zero_optimization": {"stage": 3, "stage3_gather_16bit_weights_on_model_save": true, "stage3_max_live_parameters": 100000000.0, "stage3_max_reuse_distance": 100000000.0, "stage3_prefetch_bucket_size": 1000000.0, "stage3_param_persistence_threshold": 100000.0, "offload_optimizer": {"device": "cpu", "pin_memory": false, "buffer_count": 2, "fast_init": false}, "offload_param": {"device": "cpu", "pin_memory": false, "buffer_count": 2, "buffer_size": 10000000.0, "max_in_cpu": 200000000.0}, "allgather_partitions": true, "allgather_bucket_size": 200000000.0, "reduce_scatter": true, "reduce_bucket_size": 200000000.0, "contiguous_gradients": true, "overlap_comm": true, "round_robin_gradients": true, "sub_group_size": 100000000.0}, "activation_checkpointing": {"partition_activations": true, "cpu_checkpointing": false, "contiguous_memory_optimization": true, "number_checkpoints": 2, "synchronize_checkpoint_boundary": false, "profile": false}, "optimizer": {"type": "AdamW", "params": {"lr": "auto", "betas": [0.9, 0.999], "eps": 1e-08, "weight_decay": 0.01}}, "bf16": {"enabled": true}, "fp16": {"enabled": false}, "communication_data_type": "bf16", "aio": {"block_size": 262144, "queue_depth": 4, "thread_count": 1, "single_submit": false, "overlap_events": true}, "comms_logger": {"enabled": false}, "tensorboard": {"enabled": false}, "wall_clock_breakdown": false, "memory_breakdown": false, "flops_profiler": {"enabled": false}}