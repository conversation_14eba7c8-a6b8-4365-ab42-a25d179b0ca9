#!/bin/bash

# 设置内存优化环境变量
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True
export CUDA_LAUNCH_BLOCKING=0
export TORCH_NCCL_BLOCKING_WAIT=1

# 设置DeepSpeed优化
export DEEPSPEED_ZERO_INIT=1

# 禁用PyTorch编译优化
export TORCH_COMPILE_DISABLE=1
export TORCHDYNAMO_DISABLE=1

# 限制OMP线程数减少内存使用
export OMP_NUM_THREADS=4

# 清理GPU缓存
python -c "import torch; torch.cuda.empty_cache()"

echo "🚀 启动优化的训练..."
echo "📊 内存优化设置："
echo "   - PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True"
echo "   - 评估批次大小=1"
echo "   - ZeRO Stage 3 + 激进CPU offload"
echo "   - 激活检查点 + CPU检查点"
echo ""

# 激活conda环境并启动训练
source /root/miniforge3/etc/profile.d/conda.sh
conda activate edit-pred-model
deepspeed --num_gpus=2 train_scripts/base_sft_lora.py
