import os
from vllm import LLM, SamplingParams
import pandas as pd
import Levenshtein
import sacrebleu
import json

model_path = "/data/jiangh/nju-devops/Zeta-finetune/all_params/seed-mydata-zeta-0721-sft_params/"
model_name = "my_"
# model_path = "/data/jiangh/nju-devops/LLM/zeta/"

llm = LLM(model_path)

def make_prompt(input, events):
    prompt = f'''### Instruction:
You are a code completion assistant and your task is to analyze user edits and then rewrite an excerpt that the user provides, suggesting the appropriate edits within the excerpt, taking into account the cursor location.
### User Edits:

{events}

### User Excerpt:

{input}

### Response:

'''
    return prompt

def read_jsonl(file_path):
    """
    从JSONL文件中读取数据，并根据特定标记截取prompt和label。
    """
    prompts = []
    labels = []
    
    start_marker = "<|editable_region_start|>"
    end_marker = "<|editable_region_end|>"

    with open(file_path, 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            try:
                data_line = json.loads(line)
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行跳过无效JSON: {e}")
                continue

            required_keys = ['input', 'output', 'events']
            if not all(key in data_line for key in required_keys):
                print(f"第 {line_num} 行跳过缺失字段的行（需要包含{required_keys}）")
                continue
            
            full_output_for_label = data_line['output']
            start_index = full_output_for_label.find(start_marker)
            if start_index == -1:
                print(f"警告: 第 {line_num} 行的label中未找到start_marker，已跳过。")
                continue

            focused_region = full_output_for_label[start_index:]
            end_index = focused_region.find(end_marker)
            if end_index == -1:
                print(f"警告: 第 {line_num} 行的label中未找到 '{end_marker}'，已跳过。")
                continue
            
            final_label = focused_region[:end_index + len(end_marker)]
            
            prompts.append(make_prompt(data_line['input'], data_line['events']))
            labels.append(final_label)

    return prompts, labels

datas, labels = read_jsonl("/data/jiangh/nju-devops/datasets/zeta/eval.jsonl")
sampling_params = SamplingParams(
    temperature=0,
    top_p=0.9,
    max_tokens=1024,
    n=1,
    skip_special_tokens=False,
)

print("开始批量生成文本...")
outputs = llm.generate(datas, sampling_params)
print("文本生成完毕。")

# 4. 遍历生成的结果和标签来计算分数
bleu_scores = []
Levenshtein_scores = []
bleu_sum_score, Levenshtein_sum_score, num = 0.0, 0.0, 0.0
for output, label in zip(outputs, labels):
    predicted_text = output.outputs[0].text
    if model_path == "/data/jiangh/nju-devops/LLM/zeta/":
        original_end_marker = "<|editable_region_end|>"
        output_end_index = predicted_text.find(original_end_marker)
        predicted_text = predicted_text[:output_end_index + len(original_end_marker)]
    bleu_score = sacrebleu.corpus_bleu([predicted_text], [[label]])
    Levenshtein_score = Levenshtein.ratio(predicted_text, label) * 100
    bleu_scores.append(bleu_score.score)
    Levenshtein_scores.append(Levenshtein_score)

df_data = {
    'labels': labels,
    f"{model_name}predicted": [output.outputs[0].text for output in outputs],
    f"{model_name}bleu_score": bleu_scores,
    f"{model_name}Levenshtein_score": Levenshtein_scores,
}
df = pd.DataFrame(df_data)
df.to_parquet('model_comparison_results.parquet', index=True)