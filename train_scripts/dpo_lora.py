import torch
import os
from datasets import load_dataset
from trl import DPOTrainer, DPOConfig, SFTTrainer, DataCollatorForCompletionOnlyLM
from transformers import TrainingArguments, AutoModelForCausalLM, AutoTokenizer, TrainerCallback  # Added import
import deepspeed
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from transformers import BitsAndBytesConfig

seed = 42
weight_decay = 0.01
max_seq_length = 3072
num_train_epochs = 1
learning_rate = 5e-5
per_device_train_batch_size = 1
gradient_accumulation_steps = 1
warmup_steps = 100
deepspeed_config = "./dpo_lora_ds_config.json"
save_path = "/data/jiangh/nju-devops/Zeta-finetune/dpo_params/lora_params"
model_path = "/data/jiangh/nju-devops/Zeta-finetune/sft_params"
tokenizer_path = "/data/jiangh/nju-devops/Zeta-finetune/sft_params"

# LoRA配置
lora_config = LoraConfig(
    r=256,  # 低秩矩阵的秩，越小显存占用越少（推荐8-32）
    lora_alpha=256,  # 缩放因子，通常为r的2倍
    target_modules=[  # Qwen2.5-Coder的注意力模块名称（需与模型结构匹配）
        "q_proj", "k_proj", "v_proj", "o_proj",  # 注意力的Q/K/V/O投影层
        "gate_proj", "up_proj", "down_proj"  # FFN层（可选，视效果添加）
    ],
    lora_dropout=0.05,
    bias="none",  # 不训练偏置
    task_type="CAUSAL_LM",  # 因果语言模型任务
    inference_mode=False  # 训练模式
)

# 主模型
model = AutoModelForCausalLM.from_pretrained(model_path, attn_implementation="flash_attention_2", torch_dtype=torch.bfloat16)
model.gradient_checkpointing_enable()  # 启用梯度检查点
model = prepare_model_for_kbit_training(model)  # 非量化场景下的准备
model = get_peft_model(model, lora_config)
tokenizer = AutoTokenizer.from_pretrained(model_path)
# model.train()

# ref模型
# ref_model = AutoModelForCausalLM.from_pretrained(
#     "/data/jiangh/nju-devops/Zeta-finetune/params/",
#     attn_implementation="flash_attention_2",
#     torch_dtype=torch.bfloat16,
#     low_cpu_mem_usage=True, # 节省CPU内存
# )
# ref_model.eval()

alpaca_prompt = """### Instruction:
You are a code completion assistant and your task is to analyze user edits and then rewrite an excerpt that the user provides, suggesting the appropriate edits within the excerpt, taking into account the cursor location.

### User Edits:

{}

### User Excerpt:

{}

### Response:

{}
"""

EOS_TOKEN = tokenizer.eos_token  # Must add EOS_TOKEN
original_start_marker = "<|editable_region_start|>"
original_end_marker = "<|editable_region_end|>"
def format_example(events, input, output):
    return alpaca_prompt.format(events, input, output)

def formatting_prompts_func(examples):
    events       = examples["events"]
    inputs       = examples["input"]
    outputs      = examples["output"]
    rejecteds     = examples["rejected"]
    chosen_texts = []
    rejected_texts = []
    prompt_texts = []
    for events, input, output, rejected in zip(events, inputs, outputs, rejecteds):
        output_start_index = output.find(original_start_marker)
        output_focused_region = output[output_start_index:]
        output = output_focused_region

        rejected_start_index = rejected.find(original_start_marker)
        rejected_focused_region = rejected[rejected_start_index:]
        rejected = rejected_focused_region
        # Must add EOS_TOKEN, otherwise your generation will go on forever!
        prompt_texts.append(format_example(events, input, ""))
        chosen_texts.append(output + EOS_TOKEN)
        rejected_texts.append(rejected + EOS_TOKEN)
    return { "prompt": prompt_texts, "chosen": chosen_texts, "rejected": rejected_texts}

def filter_long_sequences(examples):
    full_chosen = tokenizer(examples["prompt"] + examples["chosen"])
    full_rejected = tokenizer(examples["prompt"] + examples["rejected"])
    prompt = tokenizer(examples["prompt"])
    return len(full_chosen['input_ids']) <= max_seq_length and len(full_rejected['input_ids']) <= max_seq_length 

# 读取文件夹并自动加载该目录下的数据文件，自动识别格式,（这里明确指出读取的文件）
dataset = load_dataset("/data/jiangh/nju-devops/datasets/zeta",data_files={
    'dpo': '/data/jiangh/nju-devops/datasets/zeta/dpo.jsonl',
    'eval': '/data/jiangh/nju-devops/datasets/zeta/eval.jsonl'
})
dataset = dataset.map(formatting_prompts_func, batched=True,)
train_dataset = dataset["dpo"].filter(filter_long_sequences)
eval_dataset = dataset["eval"].filter(filter_long_sequences)

response_template = "### Response:\n\n"

trainer = DPOTrainer(
    model=model,
    # ref_model=ref_model,
    ref_model=None,
    processing_class=tokenizer,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    args=DPOConfig(
        max_length=max_seq_length,
        num_train_epochs=num_train_epochs,  # 训练的轮数
        weight_decay=weight_decay,                   # 权重衰减，用于正则化
        seed=seed,                              # 随机种子，确保结果可复现
        output_dir="./output_log",           # 输出日志和模型的目录
        report_to="none",                    # 不报告到任何地方
        eval_strategy="epoch",               # 评估策略为按步骤评估
        do_eval=True,                        # 启用评估
        ddp_find_unused_parameters=False,    # DDP模式下是否查找未使用的参数
        bf16=True,                           # 是否使用bf16精度
        fp16=False,                          # 是否使用fp16精度
        deepspeed=deepspeed_config,       # DeepSpeed配置文件路径
        gradient_checkpointing=True,         # 启用梯度检查点
        # deepspeed配置
        per_device_train_batch_size=per_device_train_batch_size,
        gradient_accumulation_steps=gradient_accumulation_steps,  
        learning_rate=learning_rate,
        # 训练损失记录
        logging_steps=10,  # 每10步记录一次
    )
)
trainer.train()
model.save_pretrained(save_path)    # 保存模型权重及配置文件(lora)
tokenizer.save_pretrained(save_path) # 保存分词器配置文件