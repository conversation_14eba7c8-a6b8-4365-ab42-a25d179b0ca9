import torch
import os
from datasets import load_dataset
from trl import DPOTrainer, DPOConfig, SFTTrainer, DataCollatorForCompletionOnlyLM,SFTConfig
from transformers import TrainingArguments, AutoModelForCausalLM, AutoTokenizer, TrainerCallback  # Added import
import deepspeed
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from transformers import BitsAndBytesConfig

seed = 42
max_seq_length = 3072
num_train_epochs = 1
learning_rate = 1e-4
per_device_train_batch_size = 2
gradient_accumulation_steps = 1
warmup_steps = 100
deepspeed_config = "./sft_lora_ds_config.json"
save_path = "/data/jiangh/nju-devops/Zeta-finetune/sft_params/lora_params"
model_path = "/data/jiangh/nju-devops/LLM/Qwen2.5-Coder-7B-Instruct/"
tokenizer_path = "/data/jiangh/nju-devops/LLM/Qwen2.5-Coder-7B-Instruct/"

# LoRA配置
lora_config = LoraConfig(
    r=256,  # 低秩矩阵的秩，越小显存占用越少（推荐8-32）
    lora_alpha=256,  # 缩放因子，通常为r的2倍
    target_modules=[  # Qwen2.5-Coder的注意力模块名称（需与模型结构匹配）
        "q_proj", "k_proj", "v_proj", "o_proj",  # 注意力的Q/K/V/O投影层
        "gate_proj", "up_proj", "down_proj"  # FFN层（可选，视效果添加）
    ],
    lora_dropout=0.0,
    bias="none",  # 不训练偏置
    task_type="CAUSAL_LM",  # 因果语言模型任务
    inference_mode=False  # 训练模式
)
# /data/jiangh/nju-devops/Zeta-finetune/
model = AutoModelForCausalLM.from_pretrained(model_path, attn_implementation="flash_attention_2", torch_dtype=torch.bfloat16)
model.gradient_checkpointing_enable()  # 启用梯度检查点,会增加训练时间
# 将模型中的 LayerNorm 等层的数据类型转换为 float32，以增加训练的稳定性。即使模型主体是 bfloat16，某些特定操作使用全精度计算会更稳定。
# 原本是为了量化训练，但不量化也支持，确保模型的某些部分是可训练的，并进行一些兼容性处理，为后续应用 LoRA 等 PEFT 方法做好准备。
model = prepare_model_for_kbit_training(model)  
model = get_peft_model(model, lora_config)  # 应用peft(lora)
tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)

EOS_TOKEN = tokenizer.eos_token
original_start_marker = "<|editable_region_start|>"
original_end_marker = "<|editable_region_end|>"

def formatting_prompts_func(examples):
    instructions = [f"User Edits:\n{evt}\n\nUser Excerpt:\n{inp}" for evt, inp in zip(examples["events"], examples["input"])]
    outputs = examples["output"] # 获取输出并且添加结束符
    texts = []
    for instruction, output in zip(instructions, outputs):
        start_index = output.find(original_start_marker)
        output = output[start_index:]  # 截取从start_marker开始的部分
        end_index = output.find(original_end_marker)
        output = output[:end_index + len(original_end_marker)]  # 包含结束标记本身

        # 构建符合官方模板的对话列表
        messages = [
            {"role": "system", "content": "You are an AI code completion assistant. Your sole function is to predict and output the final intended code based on the provided context. You will be given \"User Edits\" (a log of changes) and a \"User Excerpt\" (the current code). The \"User Excerpt\" will contain a marked editable region and the user's cursor position. Your output must be only the complete, final code for the editable region, enclosed by <|editable_region_start|> and <|editable_region_end|>. Do not include any other text, explanation, or characters."},
            {"role": "user", "content": instruction},
            {"role": "assistant", "content": output+ EOS_TOKEN}  # 添加结束符
        ]
        # 使用tokenizer的内置模板，并自动添加 special tokens (包括EOS)
        text = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=False)
        texts.append(text)
    return {"text": texts}

def filter_long_sequences(examples):
    tokenized = tokenizer(examples["text"])
    return len(tokenized['input_ids']) <= max_seq_length

dataset = load_dataset("/data/jiangh/nju-devops/datasets/zeta")
dataset = dataset.map(formatting_prompts_func, batched=True,)
train_dataset = dataset["train"].filter(filter_long_sequences)  # 过滤掉token长度过长的，但是train_dataset还是文字
eval_dataset = dataset["eval"].filter(filter_long_sequences)

class CustomCallback(TrainerCallback):
    def on_log(self, args, state, control, logs=None, **kwargs):
        if logs is not None:
            print(f"训练损失: {logs.get('loss', 'N/A')}")  # 输出训练损失

trainer = SFTTrainer(
    model=model,
    processing_class=tokenizer,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    # data_collator=collator,当使用了tokenizer.apply_chat_template时，不需要额外的data_collator
    args=SFTConfig(
        dataset_text_field="text",  # 指定数据集中使用的文本字段
        weight_decay=0.01,  # 设置权重衰减
        num_train_epochs=num_train_epochs,
        seed=seed,
        output_dir="./output_log",
        report_to="none",
        eval_strategy="epoch",
        do_eval=True,
        ddp_find_unused_parameters=False,
        bf16=True,
        fp16=False,
        deepspeed=deepspeed_config,
        gradient_checkpointing=True,
        max_seq_length=max_seq_length,
        # deepspeed的配置
        # 每个显卡间隔per_device_train_batch_size*gradient_accumulation_steps个样本才更新一次参数
        per_device_train_batch_size=per_device_train_batch_size,  # 每个设备的批量大小
        gradient_accumulation_steps=gradient_accumulation_steps,  # 累积梯度的步数
        learning_rate=learning_rate,
        # 训练损失记录
        logging_steps=10,  # 每10步记录一次
        # logging_dir="./logs",  # 日志保存目录
    ),
    callbacks=[CustomCallback()]  # 添加自定义回调，每个epoch输出训练损失
)
trainer.train()
model.save_pretrained(save_path)    # 保存模型权重及配置文件(lora)
tokenizer.save_pretrained(save_path) # 保存分词器配置文件