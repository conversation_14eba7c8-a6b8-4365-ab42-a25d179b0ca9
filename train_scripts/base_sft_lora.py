import torch
import os
from datasets import load_dataset

# 禁用PyTorch编译优化以减少内存使用
torch._dynamo.config.suppress_errors = True
torch._dynamo.config.disable = True
from trl import DPOTrainer, DPOConfig, SFTTrainer, DataCollatorForCompletionOnlyLM,SFTConfig
from transformers import TrainingArguments, AutoModelForCausalLM, AutoTokenizer, TrainerCallback 
import deepspeed
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from transformers import BitsAndBytesConfig

seed = 42
max_seq_length = 3072
learning_rate = 1e-4
per_device_train_batch_size = 2
gradient_accumulation_steps = 1
deepspeed_config = "./deepspeed_config/sft_lora_ds_config.json"

# 修改以下路径为你的实际路径
save_path = "/opt/models/Seed-Coder-8B-Base-sft/"
model_path = "/opt/models/Seed-Coder-8B-Base"
tokenizer_path = "/opt/models/Seed-Coder-8B-Base"
dataset_flag = "zeta"   # "zeta" or "mydata"
num_train_epochs = 2    # 根据数据调整训练轮数

lora_config = LoraConfig(
    r=256,
    lora_alpha=256,
    target_modules=[
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ],
    lora_dropout=0.0,
    bias="none",
    task_type="CAUSAL_LM",
    inference_mode=False
)

# Load tokenizer first
tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)

# Add padding token if it doesn't exist
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Load model with proper device mapping for DeepSpeed
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    attn_implementation="flash_attention_2",
    torch_dtype=torch.bfloat16,
    trust_remote_code=True
)

# Enable gradient checkpointing
model.gradient_checkpointing_enable()

# Apply LoRA directly without prepare_model_for_kbit_training since we're not using quantization
model = get_peft_model(model, lora_config)

# Resize token embeddings if tokenizer has new tokens
if len(tokenizer) > model.config.vocab_size:
    model.resize_token_embeddings(len(tokenizer))

# Ensure model is in training mode
model.train()

alpaca_prompt = """### Instruction:
You are a code completion assistant and your task is to analyze user edits and then rewrite an excerpt that the user provides, suggesting the appropriate edits within the excerpt, taking into account the cursor location.

### User Edits:

{}

### User Excerpt:

{}

### Response:

{}
"""

EOS_TOKEN = tokenizer.eos_token
original_start_marker = "<|editable_region_start|>"
original_end_marker = "<|editable_region_end|>"

def format_example(events, input, output):
    return alpaca_prompt.format(events, input, output)

def formatting_prompts_func(examples):
    events = examples["events"]
    inputs = examples["input"]
    outputs = examples["output"]
    texts = []
    for events, input, output in zip(events, inputs, outputs):
        output_start_index = output.find(original_start_marker)
        output_focused_region = output[output_start_index:]
        output_end_index = output_focused_region.find(original_end_marker)
        output = output_focused_region[:output_end_index + len(original_end_marker)]
        text = format_example(events, input, output) + EOS_TOKEN
        texts.append(text)
    return {"text": texts}

def filter_long_sequences(examples):
    tokenized = tokenizer(examples["text"])
    return len(tokenized['input_ids']) <= max_seq_length

if dataset_flag == "zeta":
    dataset = load_dataset("json", data_files={
        "train": "./datasets/train.jsonl",
        "eval": "./datasets/eval.jsonl"
    })
elif dataset_flag == "mydata":
    dataset = load_dataset("json", data_files={
        "train": "./data_synthesis/final_json_data/train.jsonl",
        "eval": "./data_synthesis/final_json_data/eval.jsonl"
    })
else:
    raise ValueError("Unsupported dataset flag. Use 'zeta' or 'mydata'.")

dataset = dataset.map(formatting_prompts_func, batched=True, remove_columns=dataset["train"].column_names)
train_dataset = dataset["train"].filter(filter_long_sequences)
eval_dataset = dataset["eval"].filter(filter_long_sequences)

response_template = "### Response:\n\n"
collator = DataCollatorForCompletionOnlyLM(response_template, tokenizer=tokenizer)

class CustomCallback(TrainerCallback):
    def on_log(self, args, state, control, logs=None, **kwargs):
        if logs is not None:
            print(f"训练损失: {logs.get('loss', 'N/A')}")

    def on_evaluate(self, args, state, control, logs=None, **kwargs):
        # 评估后清理GPU缓存
        import torch
        torch.cuda.empty_cache()
        if logs is not None:
            print(f"评估完成，清理GPU缓存")

# Create training arguments with DeepSpeed configuration
training_args = SFTConfig(
    dataset_text_field="text",  # Use text field for modern approach
    weight_decay=0.01,
    num_train_epochs=num_train_epochs,
    seed=seed,
    output_dir="./checkpoints",
    # save_strategy="steps",           # 按步数保存 或 "epoch" 按轮次保存
    # save_steps=50,                  # 每500步保存一次
    report_to="none",
    eval_strategy="no",  # 关闭评估避免内存问题
    do_eval=False,
    per_device_eval_batch_size=1,  # 减小评估批次大小
    eval_accumulation_steps=1,  # 评估时也使用梯度累积
    prediction_loss_only=True,  # 只计算loss，不保存logits
    ddp_find_unused_parameters=False,
    bf16=True,
    fp16=False,
    deepspeed=deepspeed_config,
    gradient_checkpointing=True,
    max_seq_length=max_seq_length,
    per_device_train_batch_size=per_device_train_batch_size,
    gradient_accumulation_steps=gradient_accumulation_steps,
    learning_rate=learning_rate,
    dataloader_pin_memory=False,  # Important for DeepSpeed
    remove_unused_columns=True,  # Remove unused columns to avoid data collator issues
    save_safetensors=True
    # logging_steps=10,
    # logging_dir="./logs",
)

trainer = SFTTrainer(
    model=model,
    processing_class=tokenizer,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
    data_collator=collator,
    args=training_args,
    callbacks=[CustomCallback()]
)

trainer.train()
trainer.train(resume_from_checkpoint="./checkpoints/checkpoint-50")

# Save the model
model.save_pretrained(save_path)
tokenizer.save_pretrained(save_path)

# print("Training completed! Starting AWQ quantization...")

# # Run AWQ quantization
# import subprocess
# import sys

# try:
#     # Run the AWQ quantization script
#     result = subprocess.run([
#         sys.executable, "quantize_to_awq.py"
#     ], capture_output=True, text=True, cwd=".")

#     if result.returncode == 0:
#         print("✅ AWQ quantization completed successfully!")
#         print(result.stdout)
#     else:
#         print("❌ AWQ quantization failed:")
#         print(result.stderr)

# except Exception as e:
#     print(f"❌ Error running AWQ quantization: {str(e)}")
#     print("You can run quantization manually later with: python quantize_to_awq.py")