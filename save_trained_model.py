#!/usr/bin/env python3
"""
从训练输出中恢复并保存LoRA模型
"""

import torch
import os
from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import LoraConfig, get_peft_model
import glob

# 配置
model_path = "/opt/models/Seed-Coder-8B-Base"
tokenizer_path = "/opt/models/Seed-Coder-8B-Base"
save_path = "./lora_params/"
output_dir = "./output_log"

lora_config = LoraConfig(
    r=256,
    lora_alpha=256,
    target_modules=[
        "q_proj", "k_proj", "v_proj", "o_proj",
        "gate_proj", "up_proj", "down_proj"
    ],
    lora_dropout=0.0,
    bias="none",
    task_type="CAUSAL_LM",
    inference_mode=False
)

def find_latest_checkpoint():
    """查找最新的checkpoint"""
    checkpoint_dirs = glob.glob(os.path.join(output_dir, "checkpoint-*"))
    if not checkpoint_dirs:
        return None
    
    # 按checkpoint编号排序，取最新的
    checkpoint_dirs.sort(key=lambda x: int(x.split('-')[-1]))
    return checkpoint_dirs[-1]

def save_model_from_checkpoint():
    """从checkpoint保存模型"""
    print("🔍 查找checkpoint...")
    
    checkpoint_dir = find_latest_checkpoint()
    if checkpoint_dir:
        print(f"✅ 找到checkpoint: {checkpoint_dir}")
        
        # 从checkpoint加载模型
        print("📥 从checkpoint加载模型...")
        try:
            # 加载tokenizer
            tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            # 加载基础模型
            base_model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.bfloat16,
                device_map="auto"
            )
            
            # 应用LoRA配置
            model = get_peft_model(base_model, lora_config)
            
            # 加载训练好的权重
            adapter_weights = torch.load(
                os.path.join(checkpoint_dir, "adapter_model.bin"),
                map_location="cpu"
            )
            model.load_state_dict(adapter_weights, strict=False)
            
            # 保存模型
            print(f"💾 保存模型到 {save_path}")
            os.makedirs(save_path, exist_ok=True)
            model.save_pretrained(save_path)
            tokenizer.save_pretrained(save_path)
            
            print("✅ 模型保存成功!")
            return True
            
        except Exception as e:
            print(f"❌ 从checkpoint加载失败: {e}")
            return False
    else:
        print("❌ 未找到checkpoint文件")
        return False

def save_model_manually():
    """手动重新训练并保存（如果没有checkpoint）"""
    print("⚠️  未找到checkpoint，需要重新运行训练")
    print("请运行以下命令重新训练:")
    print("./run_training_optimized.sh")
    return False

if __name__ == "__main__":
    print("🚀 开始恢复训练好的模型...")
    
    success = save_model_from_checkpoint()
    
    if not success:
        save_model_manually()
    else:
        print(f"\n🎉 模型已保存到: {save_path}")
        print("现在可以运行AWQ量化:")
        print("python quantize_to_awq.py")
