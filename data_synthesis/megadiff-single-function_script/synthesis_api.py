import difflib
from datasets import load_dataset
import json
from openai import OpenAI
import pandas as pd 
import pyarrow      

# 定义过滤参数
max_len_input = 2048    # 
max_diff_lines = 50     # zeta官方原数据集最大为74
num_proc = 4           # 进程数量
# 合成模型参数synthetic model
# synthetic_api_key="sk-036c86c5b9974f56820c68cae7d7e73f"
# synthetic_base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
synthetic_api_key="AIzaSyD6elrYPzgpb7IB2FU65TfLDuGXJ_PK6ZU"
synthetic_base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
synthetic_model_name = "qwen2.5-coder-7b-instruct"  
synthetic_temperature=0.0         # 提高创造性
synthetic_max_tokens=4096          # 限制输出长度为
synthetic_presence_penalty=0.2    # 鼓励引入新概念
synthetic_frequency_penalty=0.5   # 减少重复词语
# 评分模型eval_model
eval_api_key="AIzaSyD6elrYPzgpb7IB2FU65TfLDuGXJ_PK6ZU"
eval_base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
eval_model_name = "qwen2.5-coder-7b-instruct"  # 
eval_temperature=0.0         # 提高创造性
eval_max_tokens=4096          # 限制输出长度为
eval_presence_penalty=0.2    # 鼓励引入新概念
eval_frequency_penalty=0.5   # 减少重复词语
# 数据集设置
dataset_name = "ASSERT-KTH/megadiff-single-function"
dataset = load_dataset(dataset_name, split='train')


def filter_union(dataset):
    # --- 第一次过滤：按函数字符长度 ---
    print(f"原始数据集大小: {len(dataset)}")

    def filter_by_length(example):
        return (
            len(example["buggy_function"]) <= max_len_input
            and len(example["fixed_function"]) <= max_len_input
        )

    print(f"\n第1步：按函数最大字符长度 ({max_len_input}) 进行过滤...")
    len_filtered_dataset = dataset.filter(filter_by_length, num_proc=num_proc)
    print(f"过滤后大小: {len(len_filtered_dataset)}")


    # --- 第二次过滤：按 diff 总行数 ---
    def filter_by_total_diff_lines(example):
        buggy_lines = example['buggy_function'].splitlines()
        fixed_lines = example['fixed_function'].splitlines()
        diff = difflib.unified_diff(buggy_lines, fixed_lines, lineterm='')
        return len(list(diff)) <= max_diff_lines

    print(f"\n第2步：按 Diff 最大总行数 ({max_diff_lines}) 进行二次过滤...")
    total_lines_filtered_dataset = len_filtered_dataset.filter(filter_by_total_diff_lines, num_proc=num_proc)
    print(f"过滤后大小: {len(total_lines_filtered_dataset)}")


    # --- 第三次过滤：按变更结构排除简单修改 (修正版) ---
    def filter_by_change_structure(example):
        """
        使用 SequenceMatcher 分析变更结构。
        1. 如果有多于一个不连续的变更块，则保留。
        2. 如果只有一个变更块，则检查它是否为简单的单行增/删/替换，如果是则过滤。
        """
        buggy_lines = example['buggy_function'].splitlines()
        fixed_lines = example['fixed_function'].splitlines()
        
        # 使用 SequenceMatcher 来获取操作码 (opcodes)
        matcher = difflib.SequenceMatcher(None, buggy_lines, fixed_lines)
        
        # 获取所有非 'equal' 的变更操作码
        change_opcodes = [op for op in matcher.get_opcodes() if op[0] != 'equal']
        
        # CASE 1: 如果变更块数量 > 1，说明是多处独立的修改，正是我们想保留的
        if len(change_opcodes) > 1:
            return True
            
        # CASE 2: 如果只有一个变更块，我们需要分析这个块本身是否过于简单
        if len(change_opcodes) == 1:
            op = change_opcodes[0]
            tag, i1, i2, j1, j2 = op
            
            deleted_count = i2 - i1
            added_count = j2 - j1
            
            # 定义简单的单行修改模式
            is_single_add = (added_count == 1 and deleted_count == 0)
            is_single_del = (added_count == 0 and deleted_count == 1)
            is_single_replace = (added_count == 1 and deleted_count == 1)
            
            # 如果这个唯一的变更块是简单修改，则过滤掉 (返回 False)
            if is_single_add or is_single_del or is_single_replace:
                return False
            # 否则，这个单块修改也足够复杂，予以保留
            else:
                return True
                
        # CASE 3: 如果没有任何变更块，也过滤掉
        return False

    print(f"\n第3步：按变更【结构】排除单块、单行修改...")
    # 在第二次过滤的结果上继续操作
    final_dataset = total_lines_filtered_dataset.filter(filter_by_change_structure, num_proc=num_proc)
    print(f"最终过滤后大小: {len(final_dataset)}")

    # --- 结果汇总 ---
    print("\n--- 过滤流程总结 ---")
    print(f"原始数据集大小: {len(dataset)}")
    print(f"第1次过滤后 (函数长度 <= {max_len_input}): {len(len_filtered_dataset)}")
    print(f"第2次过滤后 (Diff总行数 <= {max_diff_lines}): {len(total_lines_filtered_dataset)}")
    print(f"第3次过滤后 (按结构过滤): {len(final_dataset)}")
    print("\n--- 过滤完毕 ---")
    return final_dataset

final_dataset = filter_union(dataset)

# 合成模型
synthetic_system_prompt=f'''
您是一位顶尖的代码差异分析专家，擅长洞察开发者的思维过程。您的核心任务是基于旧代码和新代码，预测开发者在进行修改时最先进行的、符合直觉的几个初始编辑步骤，并以此生成一个结构化的训练样本。
请严格遵循“先思考，后作答”的原则，完成以下任务。
第一步:思维链分析
    在生成最终结果前，请先一步步写下您的分析过程。这部分内容是您得出最终答案的依据。
    a.意图分析:一句话总结本次代码变更的核心目的(如:修复bug、增加功能、重构等)。
    b.初始编辑识别:从所有代码差异中，识别出开发者最可能先动手的 1 到 3 个关键编辑。这些编辑应当是实现最终意图的逻辑起点,且不能是全部的编辑,而是只包含部分编辑的中间状态。
    c.标记范围定义:明确指出为了包裹所有识别出的初始编辑，<|editable_region_start|> 和 <|editable_region_end|> 标记应放置在哪个具体位置，确保它形成一个包含所有变更的最小连续代码块。
第二步：生成结构化输出
在完成思维链分析后，将所有内容整合到一个 JSON 对象中。这是您唯一的最终输出。
标记规则：
    1.三个代码片段都必须且只能且必须分别使用一对 <|editable_region_start|> 和 <|editable_region_end|> 标记。
    2.这对标记必须包裹住一个包含了所有变更点的最小连续区域。即使变更点之间有未修改的代码，也必须被包含在内。
    3.三个代码片段中的标记区域必须是逻辑对应的。
代码生成规则：
old_code: 原始代码,但需要<|editable_region_start|> 和 <|editable_region_end|> 标记。
intermediate_code: 仅应用了您在“初始编辑识别”中确定的 1 到 3 个变更后的代码。此代码必须是未完成状态,且需要<|editable_region_start|> 和 <|editable_region_end|> 标记。
new_code: 完整新代码,但需要<|editable_region_start|> 和 <|editable_region_end|> 标记。。
你可以如同下面的示例一般使用标记：
if not self.pool:
    await self.connect()
<|editable_region_start|>
async with self.pool.acquire() as conn:
    tx = await conn.transaction()
    try:
        yield conn
    except Exception as e:
        await tx.rollback()
        self.logger.error(f"Transaction failed: {{e}}")
        raise
    else:
        await tx.commit()

async def execute_batch(self, queries: List[str]) -> None:
    async with self.transaction() as pool:
<|editable_region_end|>
        async with pool.acquire() as conn:
现在,请根据我提供的数据样本,开始您的工作,并严格按照以下JSON格式返回您的最终结果,并且格外注重<|editable_region_start|> 和 <|editable_region_end|> 标记。
{{
    "old_code": "在这里放入带标记的旧代码字符串。包括<|editable_region_start|> 和 <|editable_region_end|>",
    "intermediate_code": "在这里放入带标记的中间代码字符串。包括<|editable_region_start|> 和 <|editable_region_end|>",
    "new_code": "在这里放入带标记的新代码字符串。包括<|editable_region_start|> 和 <|editable_region_end|>"
}}
'''

eval_system_prompt=f'''
您是一位专业的代码审查专家，负责评估一个AI代码分析模型的输出。
这个AI模型的任务是，在给定“旧代码”和“新代码”后，生成一个“中间代码”。这个“中间代码”应该代表一个真实开发者在将旧代码修改为新代码时，最可能先动手的 1 到 3 个初始编辑步骤。
理想的“中间代码”应该：
1.  只包含部分修改：它必须是一个未完成的、过渡性的代码状态，而不是最终的完整修改。
2.  符合修改直觉：它所体现的修改应该是逻辑上的起点，是开发者最先会想到并着手修改的地方。
3.  数量有限：它应该只包含 1 到 3 个关键的初始编辑。
4.  标记合规：三个代码片段（old_code、intermediate_code、new_code）必须且只能分别使用一对 <|editable_region_start|> 和 <|editable_region_end|> 标记；标记必须包裹住包含所有变更点的最小连续区域（即使变更点之间有未修改代码也需包含）；三个代码片段的标记区域必须逻辑对应。

您的评审任务是：
1.  独立分析：请您先独立分析下方提供的“旧代码”和“新代码”，理解从旧到新的完整变更，并识别所有变更点的位置和范围。
2.  评估“中间代码”：将您的分析与“待评估的中间代码”进行比较。判断这个中间代码是否出色地捕捉了开发者最可能进行的、符合直觉的初始编辑步骤？同时验证标记是否符合规则。
    高分场景：中间代码准确反映 1-3 个符合直觉的初始修改，且标记数量正确、范围覆盖最小连续变更区域、三个代码片段标记区域逻辑对应。
    低分场景：中间代码包含全部修改，或标记缺失/多余/范围过大过小/三个代码片段标记区域不对应。
    注意：单纯的缩进或格式调整不应影响您的核心判断，但标记合规性需严格检查。
现在请针对给出的样本做出你的评估。
理由：
[在这里分步阐述详细判断过程，需包含对修改步骤合理性和标记合规性的分析（如：是否使用一对标记？标记范围是否覆盖所有变更点？三个代码片段标记区域是否对应？）]
评分：
[在这里给出一个 0 到 10 的整数评分。]
是否通过：
[当评分大于等于6分,则回答"是",否则回答"否"，不能有其它的多余内容]
'''

synthetic_client = OpenAI(
    api_key=synthetic_api_key,
    base_url=synthetic_base_url,
)

eval_client = OpenAI(
    api_key=eval_api_key,
    base_url=eval_base_url,
)

def extract_json(text: str):
    """
    从文本中查找并提取第一个遇到的、完整的JSON对象。
    
    这个函数会遍历文本，尝试从每个字符开始解析JSON。
    它能正确处理嵌套结构，并返回第一个成功解析的JSON对象。

    Args:
        text: 包含JSON的原始字符串。

    Returns:
        解析后的Python对象（dict或list），如果未找到则返回 None。
    """
    # 创建一个JSON解码器实例
    decoder = json.JSONDecoder()
    
    # 开始搜索的位置
    pos = 0
    while pos < len(text):
        # 查找下一个可能的JSON起始字符
        # 使用 find 而不是 index，因为找不到时会返回-1而不是报错
        start_bracket_pos = text.find('{', pos)
        start_square_pos = text.find('[', pos)

        # 确定第一个遇到的起始符是哪个
        if start_bracket_pos != -1 and (start_square_pos == -1 or start_bracket_pos < start_square_pos):
            start_pos = start_bracket_pos
        elif start_square_pos != -1:
            start_pos = start_square_pos
        else:
            # 文本中再也找不到 '{' 或 '['
            return None

        try:
            # 尝试从找到的起始位置解码JSON
            # raw_decode 会返回解析的对象和下一个字符的索引
            json_obj, end_pos = decoder.raw_decode(text, start_pos)
            return json_obj
        except json.JSONDecodeError:
            # 如果解码失败，说明这个 '{' 或 '[' 不是一个有效JSON的开始
            # 我们从这个无效的起始符之后继续搜索
            pos = start_pos + 1
            
    return None


# 新增：初始化列表用于存储JSON数据
json_data_list = []

for num,data in enumerate(final_dataset):
    if num>2:
        break
    try:
        raw_code_original = data['buggy_function']
        raw_code_revise = data['fixed_function']
        synthetic_user_prompt=f'''old_content:{raw_code_original},
    new_content:{raw_code_revise}'''

        synthetic_completion = synthetic_client.chat.completions.create(
            model=synthetic_model_name,
            messages = [
                {"role": "system", "content": synthetic_system_prompt},
                {"role": "user", "content": synthetic_user_prompt}
            ],
            temperature=synthetic_temperature,         # 提高创造性
            max_tokens=synthetic_max_tokens,          # 限制输出长度为 150 tokens
            presence_penalty=synthetic_presence_penalty,    # 鼓励引入新概念
            frequency_penalty=synthetic_frequency_penalty,   # 减少重复词语
        )
        synthetic_text = synthetic_completion.choices[0].message.content
        json_obj = extract_json(synthetic_text)  # 提取JSON对象
        
        # 获取评估结果
        eval_user_prompt = f'''
        下面是你需要进行评分的样本
        {synthetic_text}
        '''
        eval_completion = eval_client.chat.completions.create(
            model=eval_model_name,
            messages = [
                {"role": "system", "content": eval_system_prompt},
                {"role": "user", "content": eval_user_prompt}
            ],
            temperature=eval_temperature,
            max_tokens=eval_max_tokens,
            presence_penalty=eval_presence_penalty,
            frequency_penalty=eval_frequency_penalty,
        )
        eval_text = eval_completion.choices[0].message.content
        print("下面是输出的评价结果：\n"+eval_text)

        # 仅当JSON有效时，添加评估结果并存储
        if json_obj:
            json_obj["eval_result"] = eval_text  # 新增评估结果字段
            json_data_list.append(json_obj)  # 存储包含评估结果的JSON对象
        continue
    except Exception as e:
        print(e)
        break

# 新增：循环结束后写入Parquet文件
if json_data_list:
    # 转换为DataFrame并保存
    df = pd.DataFrame(json_data_list)
    df.to_parquet("output.parquet", engine="pyarrow")  # 输出到当前目录的output.parquet
    print("成功将JSON数据存储为Parquet文件：output.parquet")
else:
    print("未收集到有效JSON数据，未生成Parquet文件")
