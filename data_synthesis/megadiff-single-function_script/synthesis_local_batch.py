import os
from typing import Optional, Dict, List, Any
from transformers import AutoTokenizer
import pandas as pd
from vllm import LLM,SamplingParams
from datasets import load_dataset
import Levenshtein
import json
from openai import OpenAI
import re
import difflib
import torch
model_path = "/data/jiangh/nju-devops/LLM/Qwen2.5-Coder-32B-Instruct/Qwen2.5-Coder-32B-Instruct/"
tokenizer_path = "/data/jiangh/nju-devops/LLM/Qwen2.5-Coder-32B-Instruct/Qwen2.5-Coder-32B-Instruct/"
llm=LLM(model_path,tensor_parallel_size=2) # 这里的tensor_parallel_size应该和显卡数量相同
tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)
BATCH_SIZE = 128

# --- 存储parquet的路径 ---
save_path = "/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/temp_parquet/"

# --- 过滤参数 ---
max_len_input = 4096 # 最大的字节数
max_diff_lines = 70  # 最大的diff差异行数
num_proc = 4         # 进程数量

# --- 数据集设置 ---
dataset_name = "ASSERT-KTH/megadiff-single-function"
dataset = load_dataset(dataset_name, split='train')

def filter_union(dataset):
    # --- 第一次过滤：按函数字符长度 ---
    print(f"原始数据集大小: {len(dataset)}")

    def filter_by_length(example):
        return (
            len(example["buggy_function"]) <= max_len_input
            and len(example["fixed_function"]) <= max_len_input
        )

    print(f"\n第1步：按函数最大字符长度 ({max_len_input}) 进行过滤...")
    len_filtered_dataset = dataset.filter(filter_by_length, num_proc=num_proc)
    print(f"过滤后大小: {len(len_filtered_dataset)}")

    # --- 第二次过滤：按 diff 总行数 ---
    def filter_by_total_diff_lines(example):
        buggy_lines = example['buggy_function'].splitlines()
        fixed_lines = example['fixed_function'].splitlines()
        diff = difflib.unified_diff(buggy_lines, fixed_lines, lineterm='')
        return len(list(diff)) <= max_diff_lines

    print(f"\n第2步：按 Diff 最大总行数 ({max_diff_lines}) 进行二次过滤...")
    total_lines_filtered_dataset = len_filtered_dataset.filter(filter_by_total_diff_lines, num_proc=num_proc)
    print(f"过滤后大小: {len(total_lines_filtered_dataset)}")

    # --- 第三次过滤：按变更结构排除简单修改 ---
    def filter_by_change_structure(example):
        buggy_lines = example['buggy_function'].splitlines()
        fixed_lines = example['fixed_function'].splitlines()
        matcher = difflib.SequenceMatcher(None, buggy_lines, fixed_lines)
        change_opcodes = [op for op in matcher.get_opcodes() if op[0] != 'equal']
        if len(change_opcodes) > 1:
            return True
        if len(change_opcodes) == 1:
            op = change_opcodes[0]
            tag, i1, i2, j1, j2 = op
            deleted_count = i2 - i1
            added_count = j2 - j1
            is_single_add = (added_count == 1 and deleted_count == 0)
            is_single_del = (added_count == 0 and deleted_count == 1)
            is_single_replace = (added_count == 1 and deleted_count == 1)
            if is_single_add or is_single_del or is_single_replace:
                return False
            else:
                return True
        return False

    print(f"\n第3步：按变更【结构】排除单块、单行修改...")
    final_dataset = total_lines_filtered_dataset.filter(filter_by_change_structure, num_proc=num_proc)
    print(f"最终过滤后大小: {len(final_dataset)}")

    # --- 结果汇总 ---
    print("\n--- 过滤流程总结 ---")
    print(f"原始数据集大小: {len(dataset)}")
    print(f"第1次过滤后 (函数长度 <= {max_len_input}): {len(len_filtered_dataset)}")
    print(f"第2次过滤后 (Diff总行数 <= {max_diff_lines}): {len(total_lines_filtered_dataset)}")
    print(f"第3次过滤后 (按结构过滤): {len(final_dataset)}")
    print("\n--- 过滤完毕 ---")
    return final_dataset

final_filtered_dataset = filter_union(dataset)

def extract_last_json(text: str) -> Optional[Dict[str, Any] | List[Any]]:
    end_pos = text.rfind('}')
    while end_pos != -1:
        brace_balance = 0
        start_pos = -1 
        for i in range(end_pos, -1, -1):
            char = text[i]
            if char == '}':
                brace_balance += 1
            elif char == '{':
                brace_balance -= 1
            if brace_balance == 0:
                start_pos = i
                break
        if start_pos != -1:
            potential_json_str = text[start_pos : end_pos + 1]
            try:
                return json.loads(potential_json_str)
            except json.JSONDecodeError:
                pass
        end_pos = text.rfind('}', 0, end_pos)
    return None

synthetic_system_prompt=f'''
You are a top-tier code difference analysis expert, skilled at discerning a developer's thought process.
'Initial edit part' refers to the first few intuitive, initial steps a developer takes when modifying code.
'Complete edit part' refers to all the editing steps a developer ultimately completes, i.e., the full difference between the old and new code.
'Intermediate state code' refers to the code that results from applying the 'initial edit part' to the old code.
Therefore, the 'complete edit part' should contain and be more extensive than the 'initial edit part'.
Your core task: Strictly based on the old and new code, predict the most intuitive 'initial edit part' that a developer would perform first, and based on this, generate an 'intermediate state code'. When generating the intermediate state code, you must ensure that apart from the modifications involved in the 'initial edit part', no other part of the old code is changed in any way. Ultimately, you need to integrate this intermediate state code into a structured training sample.
Step 1: Chain of Thought Analysis
    Before generating the final result, first write down your analysis process step-by-step. This section serves as the basis for your final answer.
    a. Intent Analysis: Summarize the core purpose of this code change in a single sentence (e.g., bug fix, feature addition, refactoring, etc.).
    b. 'Initial Edit Part' Identification: From all the code differences, identify the 1 to 3 key edits that a developer would most likely make first. These edits should be the logical starting point for achieving the final intent and must not constitute the entire set of edits, but rather represent a partial, intermediate state.
Step 2: Structured Output Generation
    After completing the chain of thought analysis, integrate all content into a single JSON object. This is your final output.
    Code Generation Rules: a1.intermediate_code: Based on the old code, apply only the 1 to 3 changes you identified in the 'Initial Edit Identification'. This code must be in an incomplete state, and aside from these changes, the rest of the code must be completely identical to the old code, with no other modifications.
Now, based on the data sample I provide, begin your work and strictly return your final result in the following JSON format.
{{
    "intermediate_code": "Place the intermediate code string here."
}}
'''

json_data_list = []
prompts_batch = [] # 存储一个批次的prompts
original_data_batch = [] # 存储对应的原始数据，以便后续合并
for num, data in enumerate(final_filtered_dataset):
    # 保存原始数据
    raw_code_original = data['buggy_function']
    raw_code_revise = data['fixed_function']

    # 构造用户提示
    synthetic_user_prompt = f'''old_content:\n```\n{raw_code_original}\n```\n\nnew_content:\n```\n{raw_code_revise}\n```Now, please generate the JSON of the intermediate code with tags. You must strictly follow the final editing requirements in the system prompt. And the code area other than the changed area must be the same as old_content '''
    messages = [
        {"role": "system", "content": synthetic_system_prompt},
        {"role": "user", "content": synthetic_user_prompt}
    ]
    text = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)

    # 将prompt和原始数据加入批次
    prompts_batch.append(text)
    original_data_batch.append({
        "old_content": raw_code_original,
        "new_content": raw_code_revise
    })

    # 3. 当批次达到设定的大小，或者这是最后一条数据时，进行处理
    if len(prompts_batch) >= BATCH_SIZE or num == len(final_filtered_dataset) - 1:
        print(f"\n--- 正在处理批次，当前总进度: {num+1}/{len(final_filtered_dataset)} ---")
        
        # 定义采样参数
        sampling_params = SamplingParams(
            temperature=0.0,
            max_tokens=4096,  # 这里max_tokens虽然=4096，但实际还要考虑开头设置的字节数量
            presence_penalty=0.0,
            frequency_penalty=0.0,
        )

        try:
            # 4. 一次性生成整个批次的结果
            print(f"调用合成模型，处理 {len(prompts_batch)} 个样本...")
            batch_completions = llm.generate(
                prompts_batch,
                sampling_params=sampling_params,
            )
            print("批处理完成!")

            # 5. 遍历批次结果并进行处理
            for i, completion in enumerate(batch_completions):
                synthetic_text = completion.outputs[0].text
                original_sample = original_data_batch[i]

                try:
                    json_obj = extract_last_json(synthetic_text)
                    if json_obj is None:
                        print(f"!!! 批次中第 {i+1} 个样本解析后的JSON为空，跳过。")
                        continue
                    
                    # 将原始数据合并到JSON对象中
                    json_obj["old_content"] = original_sample["old_content"]
                    json_obj["new_content"] = original_sample["new_content"]
                    json_data_list.append(json_obj)

                except json.JSONDecodeError as e:
                    print(f"!!! 批次中第 {i+1} 个样本JSON解析失败: {e}，跳过。")
                    continue
        
        except Exception as e:
            print(f"!!! 处理批次时发生未知错误: {e}")

        # 6. 清空批次，为下一批做准备
        prompts_batch = []
        original_data_batch = []

    # 定期保存，逻辑保持不变
    if (num + 1) % 1*BATCH_SIZE == 0 and len(json_data_list) > 0:
        try:
            df = pd.DataFrame(json_data_list)
            # 文件名可以基于处理的总样本数来命名，更清晰
            output_filename = save_path + f"output_till_{num+1}.parquet"
            df.to_parquet(output_filename, engine="pyarrow")
            print(f"--- 已成功保存 {len(json_data_list)} 条数据到 {output_filename} ---")
            json_data_list = [] # 清空列表
        except Exception as e:
            print(f"写入 {output_filename} 时出错: {e}")
            continue

# 循环结束后，保存最后一批未满100条的数据
if json_data_list:
    try:
        df = pd.DataFrame(json_data_list)
        output_filename = save_path + "output_final.parquet"
        df.to_parquet(output_filename, engine="pyarrow")
        print(f"--- 已成功保存最后 {len(json_data_list)} 条数据到 {output_filename} ---")
    except Exception as e:
        print(f"写入最后的 {output_filename} 时出错: {e}")

print("\n--- 所有数据处理完毕 ---")