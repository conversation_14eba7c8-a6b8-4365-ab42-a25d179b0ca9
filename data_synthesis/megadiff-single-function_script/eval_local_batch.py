import os
from typing import Optional, Dict, List, Any
from transformers import AutoTokenizer
import pandas as pd
from vllm import LLM, SamplingParams
import json
import torch

# --- Save Updated DataFrame ---
output_dir = '/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/temp_parquet/'
output_path = os.path.join(output_dir, 'evaluated_output_test.parquet')

# --- Data Loading ---
df = pd.read_parquet("/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/temp_parquet/merge_outputs.parquet")
 
# --- Model and Tokenizer Initialization ---
model_path = "/data/jiangh/nju-devops/LLM/Qwen2.5-Coder-32B-Instruct/Qwen2.5-Coder-32B-Instruct/"
tokenizer_path = "/data/jiangh/nju-devops/LLM/Qwen2.5-Coder-32B-Instruct/Qwen2.5-Coder-32B-Instruct/"
llm = LLM(model_path, tensor_parallel_size=2)
tokenizer = AutoTokenizer.from_pretrained(tokenizer_path)

# Define batch size for processing
BATCH_SIZE = 128

# --- Helper Function for JSON Extraction ---
def extract_last_json(text: str) -> Optional[Dict[str, Any] | List[Any]]:
    # This function finds and parses the last valid JSON object in a string.
    end_pos = text.rfind('}')
    while end_pos != -1:
        brace_balance = 0
        start_pos = -1
        for i in range(end_pos, -1, -1):
            char = text[i]
            if char == '}':
                brace_balance += 1
            elif char == '{':
                brace_balance -= 1
            if brace_balance == 0:
                start_pos = i
                break
        if start_pos != -1:
            potential_json_str = text[start_pos : end_pos + 1]
            try:
                return json.loads(potential_json_str)
            except json.JSONDecodeError:
                pass  # Ignore decoding errors and try the next earlier '}'
        end_pos = text.rfind('}', 0, end_pos)
    return None

# --- Evaluation System Prompt ---
eval_system_prompt = f'''
You are an expert code reviewer specializing in the logic of code modifications. Your task is to evaluate the quality of an "intermediate_code" generated by an AI model.

The AI model's goal is to receive an "old_content" and a "new_content", predict the most intuitive "initial edits" a developer would make, and generate an "intermediate_code" based on those edits.

An ideal "intermediate_code" must have the following characteristics:
1.  **Logical Starting Point:** The included modifications should represent the most logical and primary steps a developer would take.
2.  **Incomplete State:** It must be a transitional piece of code, meaning it is neither identical to the old_content nor the new_content.
3.  **Change Isolation:** Apart from the identified "initial edits," the rest of the code must remain absolutely identical to the old_content, with no extra modifications.
4.  **Quantity Compliance:** The number of distinct edits should be between 1 and 3.

Your evaluation task is:
1.  Independently analyze the full difference between the "old_content" and the "new_content".
2.  Strictly evaluate the AI-generated "intermediate_code" based on the criteria below.

Now, for the given sample, provide your evaluation strictly in the following JSON format.

{{
  "reasoning": {{
    "logical_starting_point": "Analyze here: Are the edits in the intermediate_code the most likely first steps a developer would take? Why or why not?",
    "is_intermediate_state": "Analyze here: Is this code a valid 'intermediate' state? i.e., it differs from the old_content but has not yet incorporated all the changes from the new_content.",
    "change_isolation": "Analyze here: Are there any unauthorized or extraneous modifications beyond the intended initial edits? Is the rest of the code identical to the old_content?",
    "overall_assessment": "Analyze here: Overall, how accurately does this intermediate_code capture the developer's core thought process at the beginning of the modification?"
  }},
  "score": {{
    "total": "Provide the total integer score from 0 to 5 here.",
    "breakdown": {{
      "logical_starting_point": "1 if the edits are the most intuitive starting point, else 0.",
      "intermediate_state_validity": "1 if the code is a valid incomplete state, else 0.",
      "change_isolation": "1 if no extra changes were introduced, else 0.",
      "quantity_compliance": "1 if the number of initial edits is between 1 and 3, else 0.",
      "core_intent_capture": "1 if the core developer intent is accurately captured, else 0."
    }}
  }}
}}
'''

# --- Batch Processing Logic ---
prompts_batch = []
indices_batch = []
all_results = [] # Store tuples of (index, json_string, flag)

for index, row in df.iterrows():
    intermediate_code = row['intermediate_code']
    old_content = row['old_content']
    new_content = row['new_content']

    # Construct user prompt
    synthetic_user_prompt = f'''mark_old_content:\n```\n{old_content}\n```\n\nmark_new_content:\n```\n{new_content}\n```\n\nmark_intermediate_code:\n```\n{intermediate_code}\n```\nNow, please evaluate the intermediate_code based on the system prompt and return the evaluation result in JSON format.\n\n'''

    messages = [
        {"role": "system", "content": eval_system_prompt},
        {"role": "user", "content": synthetic_user_prompt},
    ]

    text = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)

    # Add prompt and its original index to the batch
    prompts_batch.append(text)
    indices_batch.append(index)

    # When the batch is full or it's the last item, process it
    if len(prompts_batch) >= BATCH_SIZE or index == len(df) - 1:
        print(f"\n--- Processing batch, current progress: {index + 1}/{len(df)} ---")
        
        sampling_params = SamplingParams(
            temperature=0.0,
            max_tokens=4096,
            presence_penalty=0.0,
            frequency_penalty=0.0,
        )

        # Generate completions for the entire batch
        batch_completions = llm.generate(
            prompts_batch,
            sampling_params=sampling_params,
        )

        # Process results for the batch
        for i, completion in enumerate(batch_completions):
            original_index = indices_batch[i]
            eval_text = completion.outputs[0].text
            json_obj = extract_last_json(eval_text)

            json_str_for_eval = None
            flag = None

            if json_obj is not None:
                json_str_for_eval = json.dumps(json_obj, indent=2, ensure_ascii=False)
                try:
                    total_score = int(json_obj.get("score", {}).get("total", 0))
                    flag = total_score == 5
                except (ValueError, TypeError):
                    flag = False
                
                print(f"Evaluation Result for index {original_index}: Success (Score: {total_score})")
            else:
                print(f"!!! Failed to extract JSON for evaluation at index {original_index}.")

            all_results.append((original_index, json_str_for_eval, flag))

        # Clear the batches for the next run
        prompts_batch = []
        indices_batch = []

# --- Update DataFrame with Results ---

# Sort results by index to ensure correct order
all_results.sort(key=lambda x: x[0])

# Initialize lists with None to ensure length matches DataFrame
eval_results = [None] * len(df)
flags = [None] * len(df)

for index, json_eval, flag in all_results:
    # Use the original index to place the result correctly
    # This requires that the DataFrame index is a standard 0-based range
    eval_results[index] = json_eval
    flags[index] = flag


# Add the collected data as new columns
df['json_eval'] = eval_results
df['flag'] = flags

os.makedirs(output_dir, exist_ok=True)
df.to_parquet(output_path, index=False)

print("\n--- Processing complete. ---")
print(f"Updated DataFrame saved to: {output_path}")
print("\nFirst 5 rows of the new DataFrame:")
print(df.head())