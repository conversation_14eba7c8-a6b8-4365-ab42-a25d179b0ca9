import os
import pandas as pd
from sklearn.model_selection import train_test_split

def split_train_eval_test(
    input_path: str,
    output_dir: str,
    train_ratio: float = 0.7,
    eval_ratio: float = 0.2,
    test_ratio: float = 0.1,
    random_state: int | None = None
) -> dict[str, pd.DataFrame]:
    """
    分割Parquet文件为训练集、验证集和测试集，排除intermediate_code与new_content相同的行
    
    Args:
        input_path: 输入Parquet文件路径（如："/data/jiangh/.../evaluated_diff_mark.parquet"）
        output_dir: 输出目录路径
        train_ratio: 训练集比例（0-1之间，0则不生成）
        eval_ratio: 验证集比例（0-1之间，0则不生成）
        test_ratio: 测试集比例（0-1之间，0则不生成）
        random_state: 随机种子（保证可复现）
        
    Returns:
        包含各数据集DataFrame的字典（键为"train"/"eval"/"test"）
    """
    # 验证比例有效性
    total_ratio = train_ratio + eval_ratio + test_ratio
    if not (0 <= total_ratio <= 1):
        raise ValueError(f"比例总和{total_ratio}需在0-1之间")
    if any(ratio < 0 for ratio in [train_ratio, eval_ratio, test_ratio]):
        raise ValueError("比例不能为负数")

    # 读取并过滤数据
    if not os.path.isfile(input_path):
        raise FileNotFoundError(f"输入文件不存在: {input_path}")
    df = pd.read_parquet(input_path)
    
    # 检查flag列是否存在
    if 'flag' not in df.columns:
        raise ValueError("输入数据中缺少'flag'列，请检查Parquet文件结构")
    
    # 排除intermediate_code和new_content相同的行，且只保留flag=True的行
    filtered_df = df[(df['intermediate_code'] != df['new_content']) & (df['flag'] == True)]
    if filtered_df.empty:
        raise ValueError("过滤后无有效数据（可能由于intermediate_code与new_content相同 或 flag均为False）")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 初始化结果字典
    datasets = {}
    
    # 分割逻辑（处理所有比例组合情况）
    remaining_df = filtered_df.copy()
    
    # 分割训练集
    if train_ratio > 0:
        train_size = int(len(filtered_df) * train_ratio)
        # 当比例导致分割数为0时特殊处理
        if train_size == 0:
            print(f"训练集比例{train_ratio}过小，实际分割样本数为0，跳过生成")
        else:
            datasets['train'], remaining_df = train_test_split(
                remaining_df,
                train_size=train_size,
                random_state=random_state
            )

    # 分割验证集
    if eval_ratio > 0 and not remaining_df.empty:
        eval_size = int(len(filtered_df) * eval_ratio)
        if eval_size == 0:
            print(f"验证集比例{eval_ratio}过小，实际分割样本数为0，跳过生成")
        else:
            datasets['eval'], remaining_df = train_test_split(
                remaining_df,
                train_size=eval_size,
                random_state=random_state
            )

    # 分割测试集（使用剩余数据）
    if test_ratio > 0 and not remaining_df.empty:
        test_size = int(len(filtered_df) * test_ratio)
        # 当剩余数据不足时使用全部剩余数据
        if test_size > len(remaining_df):
            test_size = len(remaining_df)
        if test_size > 0:
            datasets['test'] = remaining_df.iloc[:test_size]
            remaining_df = remaining_df.iloc[test_size:]

    # 保存并返回结果
    for name, df_set in datasets.items():
        # 检查必要列是否存在（来自mark.py的处理结果）
        required_cols = ['mark_intermediate_code', 'mark_old_content', 'mark_new_content']
        if not set(required_cols).issubset(df_set.columns):
            raise ValueError(f"数据缺少必要列，需包含：{required_cols}")

        # 列名映射：intermediate_diff -> events，mark_old_content -> input，mark_new_content -> output
        jsonl_data = df_set[required_cols].rename(columns={
            'mark_intermediate_code': 'events',
            'mark_old_content': 'input',
            'mark_new_content': 'output'
        })

        # 保存为JSONL格式（每行一个JSON对象）
        output_path = os.path.join(output_dir, f"{name}.jsonl")
        jsonl_data.to_json(
            output_path,
            orient='records',  # 按记录格式输出（每行一个对象）
            lines=True,        # 每行一个JSON（JSON Lines格式）
            force_ascii=False  # 保留中文等非ASCII字符
        )
        print(f"生成{name}集：{len(jsonl_data)}条数据 -> {output_path}")

    return datasets


# 分割为7:2:1
split_train_eval_test(
    input_path="/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/result_parquet/evaluated_diff_mark.parquet",
    output_dir="/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/result_parquet/",
    train_ratio=0.7,
    eval_ratio=0.3,
    test_ratio=0.0,
    random_state=42
)