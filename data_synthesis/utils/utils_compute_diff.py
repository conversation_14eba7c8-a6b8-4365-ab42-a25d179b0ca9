import difflib
import pandas as pd  # 新增pandas依赖用于处理Parquet文件

def compute_code_diff(original_code: str, modified_code: str) -> str:
    """
    计算两段代码的统一格式差异（Unified Diff）
    
    Args:
        original_code (str): 原始代码字符串
        modified_code (str): 修改后的代码字符串
    
    Returns:
        str: 包含差异信息的字符串（统一格式）
    """
    # 将代码字符串按行分割为列表（不保留换行符）
    original_lines = original_code.splitlines()
    modified_lines = modified_code.splitlines()
    
    # 生成统一格式的差异结果
    # fromfile/tofile参数用于在差异结果中标识文件来源
    diff_generator = difflib.unified_diff(
        original_lines,
        modified_lines,
        fromfile='original_code',
        tofile='modified_code',
        lineterm=''  # 避免额外添加换行符
    )
    
    # 将生成器转换为字符串返回
    return '\n'.join(diff_generator)

# 新增：处理单个Parquet文件的函数
def process_single_parquet(input_file: str, output_file: str) -> None:
    """
    从单Parquet文件读取数据，计算intermediate_code与old_content的diff，保存为新列
    
    Args:
        input_file (str): 输入Parquet文件路径（如："input.parquet"）
        output_file (str): 输出Parquet文件路径（如："output_with_diff.parquet"）
    """
    # 读取输入文件
    df = pd.read_parquet(input_file)
    
    # 校验必要列是否存在
    required_columns = ['intermediate_code', 'old_content']
    if not set(required_columns).issubset(df.columns):
        missing = [col for col in required_columns if col not in df.columns]
        raise ValueError(f"输入文件缺少必要列: {missing}")
    
    # 计算差异并添加新列
    df['intermediate_diff'] = df.apply(
        lambda row: compute_code_diff(row['intermediate_code'], row['old_content']),
        axis=1  # 按行应用函数
    )
    
    # 保存处理后的数据
    df.to_parquet(output_file)
    print(f"差异计算完成，结果已保存至: {output_file}")

process_single_parquet("/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/temp_parquet/evaluated_output.parquet", 
                       "/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/temp_parquet/evaluated_diff.parquet")