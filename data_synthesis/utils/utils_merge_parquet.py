from __future__ import annotations

import glob
import os
from pathlib import Path

import pandas as pd
import pyarrow.parquet as pq


def merge_parquet(
    input_dir: str | os.PathLike,
    output_path: str | os.PathLike,
    *,
    recursive: bool = False,
    keep_index: bool = False,
) -> Path:
    """
    合并目录中所有 Parquet 文件（可递归），并丢弃含 NULL 的行。

    Parameters
    ----------
    input_dir : str | Path
        待遍历目录。
    output_path : str | Path
        输出文件路径。
    recursive : bool, default False
        是否递归搜索子目录。
    keep_index : bool, default False
        是否保留行索引。

    Returns
    -------
    Path
        生成的 Parquet 文件路径。
    """
    pattern = "**/*.parquet" if recursive else "*.parquet"
    files = glob.glob(os.path.join(os.fspath(input_dir), pattern), recursive=recursive)
    if not files:
        raise FileNotFoundError(f"No parquet files found in {input_dir}")

    frames: list[pd.DataFrame] = []
    for file in files:
        # 直接用 pyarrow 读取再转 pandas 速度更快
        table = pq.read_table(file)
        df = table.to_pandas().dropna()
        if not df.empty:
            frames.append(df)

    if not frames:
        raise ValueError("All data frames are empty after dropping NULL rows.")

    merged = pd.concat(frames, ignore_index=True)

    merged.to_parquet(output_path, index=keep_index)
    return Path(output_path)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Merge parquet files")
    parser.add_argument("input_dir", help="目录路径，遍历其中的 parquet 文件")
    parser.add_argument("output_path", help="输出 parquet 文件名")
    parser.add_argument("-r", "--recursive", action="store_true", help="递归搜索子目录")
    parser.add_argument("--keep-index", action="store_true", help="保留行索引")
    args = parser.parse_args()

    merge_parquet(args.input_dir, args.output_path, recursive=args.recursive, keep_index=args.keep_index)
    print(f"Merged parquet saved to {args.output_path}")