import os
import re
import pandas as pd

def process_strings_by_line(str1, str2, str3):
    """
    按行比较三个字符串，找出公共的行前缀和行后缀，
    然后在不同部分的前后插入不带缩进的标记行。

    Args:
        str1 (str): 第一个多行字符串。
        str2 (str): 第二个多行字符串。
        str3 (str): 第三个多行字符串。

    Returns:
        tuple: 一个包含三个处理后字符串的元组。
    """
    # 将输入字符串按行分割成列表，保留换行符
    # splitlines(keepends=False) 更容易处理列表索引
    lines1 = str1.splitlines()
    lines2 = str2.splitlines()
    lines3 = str3.splitlines()

    inputs = [lines1, lines2, lines3]
    
    # --- 1. 计算公共行前缀的行数 ---
    prefix_line_count = 0
    # 以最短的文本为基准进行迭代
    min_len = min(len(l) for l in inputs)
    for i in range(min_len):
        # 检查当前行的内容在三个输入中是否完全相同
        if lines1[i] == lines2[i] and lines1[i] == lines3[i]:
            prefix_line_count += 1
        else:
            # 一旦有任何不匹配，立即停止
            break
            
    # --- 2. 计算公共行后缀的行数 ---
    suffix_line_count = 0
    for i in range(1, min_len + 1):
        # 从后向前比较，索引为 -i
        if lines1[-i] == lines2[-i] and lines1[-i] == lines3[-i]:
            suffix_line_count += 1
        else:
            break

    # --- 3. 构造结果 ---
    result_strings = []
    start_marker = "<|editable_region_start|>"
    end_marker = "<|editable_region_end|>"
    
    for current_lines in inputs:
        total_lines = len(current_lines)
        
        # 检查公共前后缀是否重叠或相邻
        if prefix_line_count + suffix_line_count >= total_lines:
            # 如果重叠，说明可编辑区域为空。
            # 这种情况通常发生在文本很短或者几乎完全相同时
            prefix_part = current_lines[:prefix_line_count]
            suffix_part = current_lines[total_lines-suffix_line_count:]

            # 在前缀后直接插入标记对
            # 如果没有前缀，标记就在最前面
            # 注意：这里需要更精细地处理，避免重复添加行
            # 简单的处理方式是，把可编辑区域视为空
            final_parts = prefix_part + [start_marker, end_marker] + suffix_part

        else:
            # 正常情况，前后缀不重叠
            prefix_part = current_lines[:prefix_line_count]
            middle_part = current_lines[prefix_line_count : total_lines - suffix_line_count]
            suffix_part = current_lines[total_lines - suffix_line_count:]
            
            final_parts = prefix_part + [start_marker] + middle_part + [end_marker] + suffix_part
        
        # 使用 '\n' 将所有部分连接成一个最终的字符串
        result_strings.append('\n'.join(final_parts))
        
    return tuple(result_strings)


def process_parquet_files(input_dir, output_file):
    # 获取指定目录下所有.parquet文件
    parquet_files = [f for f in os.listdir(input_dir) if f.endswith('.parquet')]
    
    # 创建一个空的DataFrame，用于存储所有数据
    combined_df = pd.DataFrame()

    for parquet_file in parquet_files:
        # 读取每个parquet文件
        file_path = os.path.join(input_dir, parquet_file)
        df = pd.read_parquet(file_path)
        
        # 合并数据到一个DataFrame
        combined_df = pd.concat([combined_df, df], ignore_index=True)
    
    # 只保留三个关键列都不为 None 的行
    combined_df = combined_df.dropna(subset=['intermediate_code', 'old_content', 'new_content'])

    result_intermediate_code = []
    result_buggy_function = []
    result_fixed_function = []

    for _, row in combined_df.iterrows():
        str1 = row['intermediate_code']
        str2 = row['old_content']
        str3 = row['new_content']

        processed_str1, processed_str2, processed_str3 = process_strings_by_line(str1, str2, str3)
        
        # 添加处理后的字符串到结果列表
        result_intermediate_code.append(processed_str1)
        result_buggy_function.append(processed_str2)
        result_fixed_function.append(processed_str3)
    
    # 将处理后的数据加入到原始DataFrame中
    combined_df['mark_intermediate_code'] = result_intermediate_code
    combined_df['mark_old_content'] = result_buggy_function
    combined_df['mark_new_content'] = result_fixed_function

    # 将最终结果保存到输出文件
    combined_df.to_parquet(output_file)

    print(f"处理结果已保存到 {output_file}")

if __name__ == "__main__":
    # input_dir是一个目录,这里需要考虑显示情况
    input_dir = "/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/temp_parquet/evaluated_diff.parquet"
    output_file = "/data/jiangh/nju-devops/Zeta-finetune/data_synthesis/finaljson_data/evaluated_diff_mark.parquet"
    
    # 处理所有.parquet文件并合并结果
    process_parquet_files(input_dir, output_file)