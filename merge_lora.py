from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel
import torch

# Load base model
base_model = AutoModelForCausalLM.from_pretrained(
    "/opt/models/Seed-Coder-8B-Base",
    torch_dtype=torch.float16,
    device_map="auto"
)

# Load tokenizer
tokenizer = AutoTokenizer.from_pretrained("/opt/models/Seed-Coder-8B-Base")

# Load and merge LoRA adapter
model = PeftModel.from_pretrained(base_model, "/root/yzn/edit-pred-model/checkpoints/checkpoint-210")
merged_model = model.merge_and_unload()

# Save merged model
merged_model.save_pretrained("/opt/models/Seed-Coder-8B-Merged")
tokenizer.save_pretrained("/opt/models/Seed-Coder-8B-Merged")